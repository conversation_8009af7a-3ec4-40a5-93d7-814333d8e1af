/**
 * ============================================================================
 * AI CONTEXT: baseline-generator-core - Core Baseline Generation Engine
 * Purpose: Core baseline generation logic with performance monitoring and enterprise orchestration
 * Complexity: Complex - Enterprise baseline generation with resilient timing integration
 * AI Navigation: 4 sections, performance domain
 * Lines: Target ≤578 LOC (Specialized engine with comprehensive baseline orchestration)
 * ============================================================================
 */

/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Baseline Generator Core - Performance Baseline Generation Engine
 * @filepath server/src/platform/performance/baseline-generator/BaselineGeneratorCore.ts
 * @milestone M0.1
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02.REF-01
 * @component baseline-generator-core
 * @reference foundation-context
 * @template enhanced-component
 * @tier server
 * @context performance-context
 * @category performance-monitoring
 * @created 2025-09-13
 * @modified 2025-09-13 00:00:00 +03
 * @version 1.0.0
 *
 * @description
 * Core baseline generation engine for performance monitoring system. Handles baseline
 * calculation algorithms, performance threshold management, baseline data persistence,
 * and enterprise-grade orchestration coordination with resilient timing integration.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact code-quality,performance-architecture
 * @milestone-compliance M0.1-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on BaseTrackingService, performance-baseline-types
 * @enables baseline-generation-capabilities, performance-monitoring
 * @extends BaseTrackingService
 * @implements IPerformanceBaseline
 * @integrates-with Enhanced Orchestration Driver v6.4.0
 * @related-contexts foundation-context, performance-context
 * @governance-impact performance-architecture, baseline-generation
 * @api-classification internal
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level MEM-SAFE-002
 * @base-class MemorySafeComponent
 * @memory-boundaries strict-enforcement
 * @memory-monitoring enabled
 * @timing-resilience-level ENHANCED
 * @timing-requirements <10ms
 * @timing-fallback-strategy graceful-degradation
 * @timing-monitoring comprehensive
 * @timing-integration dual-field-pattern
 *
 * 🌐 GATEWAY INTEGRATION (v2.3)
 * @gateway-compatibility full
 * @gateway-endpoints performance-baseline-api
 * @gateway-authentication internal-service
 * @gateway-rate-limiting standard
 * @gateway-monitoring enabled
 * @gateway-documentation auto-generated
 *
 * 🔒 SECURITY CLASSIFICATION (v2.3)
 * @security-level INTERNAL
 * @data-classification performance-metrics
 * @access-control internal-systems-only
 * @encryption-requirements none
 * @audit-requirements performance-monitoring
 * @compliance-requirements enterprise-standards
 *
 * ⚡ PERFORMANCE REQUIREMENTS (v2.3)
 * @response-time-target <10ms
 * @throughput-target >1000-ops/sec
 * @memory-limit 50MB
 * @cpu-limit 15%
 * @scalability-target enterprise-grade
 * @availability-target 99.9%
 *
 * 🔄 INTEGRATION REQUIREMENTS (v2.3)
 * @integration-tier server
 * @integration-pattern enhanced-component
 * @integration-dependencies BaseTrackingService
 * @integration-endpoints baseline-generation-api
 * @integration-protocols internal-rpc
 * @integration-monitoring comprehensive
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type baseline-generator-core
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @test-coverage >95%
 * @deployment-ready true
 * @monitoring-enabled comprehensive
 * @documentation docs/platform/performance/baseline-generator/baseline-generator-core.md
 * @naming-convention PascalCase-with-descriptive-suffixes
 * @performance-monitoring real-time-with-dashboards
 * @security-compliance enterprise-grade-validated
 * @scalability-validated horizontal-scaling-tested
 *
 * 🎯 ORCHESTRATION METADATA (v2.3)
 * @orchestration-role baseline-generator
 * @orchestration-dependencies tracking-system
 * @orchestration-triggers performance-monitoring
 * @orchestration-health-check baseline-validation
 * @orchestration-scaling horizontal
 * @orchestration-recovery graceful-degradation
 *
 * 📚 VERSION HISTORY (v2.3)
 * @version-history
 * - v1.0.0 (2025-09-13): Initial implementation with enterprise baseline generation
 * @change-log
 * - 2025-09-13: Created core baseline generation engine with resilient timing
 * @migration-notes
 * - Initial implementation, no migration required
 * @compatibility-notes
 * - Compatible with Enhanced Orchestration Driver v6.4.0+
 * - Requires BaseTrackingService foundation
 * - Implements dual-field resilient timing pattern
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: External dependencies and type imports for baseline generation
// ============================================================================

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { ResilientTimer } from '../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../shared/src/base/utils/ResilientMetrics';
import {
  TPerformanceBaselineConfig,
  TPerformanceBaselineResult,
  TComponentBaselineResult,
  TPerformanceMetricsData,
  IPerformanceBaseline,
  TPerformanceThresholds,
  TBaselineStatistics,
  TPerformanceAnalysis,
  TThresholdValidationResult
} from './types/performance-baseline-types';
import {
  TTrackingConfig,
  TTrackingData,
  TValidationResult
} from '../../../../../shared/src/types/platform/tracking/tracking-types';
import * as crypto from 'crypto';

// ============================================================================
// SECTION 2: CONSTANTS & CONFIGURATION
// AI Context: Configuration constants and default values for baseline generation
// ============================================================================

/**
 * Performance constants for baseline generation
 */
const PERFORMANCE_CONSTANTS = {
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  MAX_TRACKING_RETRIES: 3,
  BASELINE_CACHE_TTL: 300000, // 5 minutes
  MAX_CONCURRENT_BASELINES: 10,
  METRICS_BUFFER_SIZE: 1000,
  VALIDATION_TIMEOUT: 5000, // 5 seconds
  CLEANUP_INTERVAL: 60000, // 1 minute
  MAX_BASELINE_AGE: 86400000 // 24 hours
} as const;

/**
 * Default configuration for baseline generation
 */
const DEFAULT_BASELINE_CONFIG: Partial<TPerformanceBaselineConfig> = {
  samplingInterval: 1000, // 1 second
  samplingDuration: 60000, // 1 minute
  thresholds: {
    responseTime: 10, // 10ms for Enhanced components
    memoryUsage: 50 * 1024 * 1024, // 50MB
    cpuUsage: 15, // 15%
    throughput: 1000 // 1000 ops/sec
  },
  environment: 'development',
  enabled: true
};

/**
 * Baseline generation error codes
 */
const BASELINE_ERROR_CODES = {
  INVALID_CONFIG: 'BASELINE_INVALID_CONFIG',
  COMPONENT_NOT_FOUND: 'BASELINE_COMPONENT_NOT_FOUND',
  SAMPLING_FAILED: 'BASELINE_SAMPLING_FAILED',
  THRESHOLD_VIOLATION: 'BASELINE_THRESHOLD_VIOLATION',
  STORAGE_ERROR: 'BASELINE_STORAGE_ERROR',
  TIMEOUT_ERROR: 'BASELINE_TIMEOUT_ERROR'
} as const;

// ============================================================================
// SECTION 3: BASELINE GENERATOR CORE IMPLEMENTATION
// AI Context: Main baseline generation engine with enterprise capabilities
// ============================================================================

/**
 * Baseline Generator Core - Specialized Baseline Generation Engine
 *
 * @description Core baseline generation engine for performance monitoring with comprehensive
 * baseline calculation algorithms, performance threshold management, and enterprise-grade
 * orchestration coordination. This class provides specialized baseline generation capabilities
 * extracted from the monolithic PerformanceBaselineGenerator.
 *
 * @example
 * ```typescript
 * // Initialize the baseline generator core
 * const baselineCore = new BaselineGeneratorCore();
 * await baselineCore.doInitialize();
 *
 * // Configure baseline generation
 * const config: TPerformanceBaselineConfig = {
 *   baselineId: 'baseline-001',
 *   name: 'Component Performance Baseline',
 *   description: 'Baseline for M0 component performance',
 *   targetComponents: ['component-1', 'component-2'],
 *   samplingInterval: 1000,
 *   samplingDuration: 60000,
 *   thresholds: {
 *     responseTime: 10,
 *     memoryUsage: 50 * 1024 * 1024,
 *     cpuUsage: 15,
 *     throughput: 1000
 *   },
 *   environment: 'production',
 *   enabled: true,
 *   metadata: {}
 * };
 *
 * // Generate baseline
 * const result = await baselineCore.generateBaseline(config);
 * console.log(`Baseline generated with ${result.componentResults.length} components`);
 * ```
 *
 * @architecture
 * **SPECIALIZED ENGINE PATTERN:**
 * - Extracted from monolithic PerformanceBaselineGenerator (1,567 LOC)
 * - Focused on baseline generation, calculation algorithms, and threshold management
 * - Implements IPerformanceBaseline interface for standardized baseline operations
 * - Integrates with Enhanced Orchestration Driver for enterprise coordination
 *
 * **MEMORY SAFETY:**
 * Extends BaseTrackingService for automatic memory leak prevention and implements
 * the dual-field resilient timing pattern required for Enhanced components with
 * <10ms performance requirements.
 *
 * @performance
 * - Target Response Time: <10ms (Enhanced component requirement)
 * - Memory Usage: <50MB baseline, <100MB peak
 * - CPU Usage: <15% sustained, <30% peak
 * - Throughput: >1000 operations/second
 *
 * @security
 * - Security Level: INTERNAL
 * - Data Classification: performance-metrics
 * - Access Control: internal-systems-only
 * - Audit Requirements: performance-monitoring
 */
export class BaselineGeneratorCore extends BaseTrackingService implements IPerformanceBaseline {
  // ============================================================================
  // DUAL-FIELD RESILIENT TIMING PATTERN (Enhanced Component Requirement)
  // ============================================================================

  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // ============================================================================
  // BASELINE GENERATION STATE
  // ============================================================================

  private readonly _baselineCache = new Map<string, TPerformanceBaselineResult>();
  private readonly _activeGenerations = new Set<string>();
  private _baselineCleanupInterval?: NodeJS.Timeout;

  /**
   * Initialize Baseline Generator Core
   * @param config - Optional tracking configuration
   */
  constructor(config?: Partial<TTrackingConfig>) {
    // ✅ Initialize memory-safe base class with baseline-specific limits
    super({
      service: {
        name: 'baseline-generator-core',
        version: '1.0.0',
        environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',
        timeout: PERFORMANCE_CONSTANTS.DEFAULT_TIMEOUT,
        retry: {
          maxAttempts: PERFORMANCE_CONSTANTS.MAX_TRACKING_RETRIES,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'performance-baseline-compliance'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 1000,
        monitoringEnabled: true,
        alertThresholds: {
          responseTime: 10,
          errorRate: 5,
          memoryUsage: 80,
          cpuUsage: 70
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: false,
        maxFileSize: 10
      },
      ...config
    });

    // ✅ Initialize resilient timing infrastructure synchronously
    this._initializeResilientTimingSync();

    this.logInfo('BaselineGeneratorCore initialized', {
      resilientTiming: 'enabled',
      cacheEnabled: true,
      performanceTarget: '<10ms'
    });
  }

  /**
   * Initialize resilient timing infrastructure synchronously
   * Required for Enhanced components with <10ms performance requirements
   */
  private _initializeResilientTimingSync(): void {
    try {
      // ✅ Initialize ResilientTimer with enterprise configuration
      this._resilientTimer = new ResilientTimer({
        enableFallbacks: true,
        maxExpectedDuration: 10, // 10ms max for Enhanced components
        unreliableThreshold: 3,
        estimateBaseline: 5 // 5ms baseline estimate
      });

      // ✅ Initialize ResilientMetricsCollector with performance monitoring
      this._metricsCollector = new ResilientMetricsCollector({
        enableFallbacks: true,
        maxMetricsAge: 30000 // 30 seconds
      });

      this.logInfo('Resilient timing infrastructure initialized', {
        timerConfig: 'enterprise-grade',
        metricsConfig: 'performance-optimized',
        fallbacksEnabled: true
      });
    } catch (error) {
      this.logError('Failed to initialize resilient timing infrastructure', { error });

      // ✅ Fallback to basic timing if resilient timing fails
      this._resilientTimer = new ResilientTimer();
      this._metricsCollector = new ResilientMetricsCollector();
    }
  }

  /**
   * Enhanced initialization with cleanup interval setup
   */
  protected async doInitialize(): Promise<void> {
    await super.doInitialize();

    // ✅ Set up periodic cleanup of expired baselines
    this._baselineCleanupInterval = setInterval(() => {
      this._cleanupExpiredBaselines();
    }, PERFORMANCE_CONSTANTS.CLEANUP_INTERVAL);

    this.logInfo('BaselineGeneratorCore initialization complete', {
      cleanupInterval: PERFORMANCE_CONSTANTS.CLEANUP_INTERVAL,
      maxConcurrentBaselines: PERFORMANCE_CONSTANTS.MAX_CONCURRENT_BASELINES
    });
  }

  /**
   * Enhanced cleanup with interval clearing
   */
  protected async doShutdown(): Promise<void> {
    // ✅ Clear cleanup interval
    if (this._baselineCleanupInterval) {
      clearInterval(this._baselineCleanupInterval);
      this._baselineCleanupInterval = undefined;
    }

    // ✅ Clear baseline cache
    this._baselineCache.clear();
    this._activeGenerations.clear();

    await super.doShutdown();

    this.logInfo('BaselineGeneratorCore cleanup complete');
  }

  // ============================================================================
  // SECTION 4: CORE BASELINE GENERATION METHODS
  // AI Context: Primary baseline generation implementation with enterprise features
  // ============================================================================

  /**
   * Generate performance baseline for specified components
   *
   * @param config - Baseline generation configuration
   * @returns Promise resolving to baseline generation result
   *
   * @performance Target: <10ms response time
   * @memory-safety Automatic cleanup and boundary enforcement
   * @resilience Graceful degradation on timing failures
   */
  async generateBaseline(config: TPerformanceBaselineConfig): Promise<TPerformanceBaselineResult> {
    const timingContext = this._resilientTimer.start();

    try {
      // ✅ Validate configuration
      const validationResult = await this._validateBaselineConfig(config);
      if (validationResult.status !== 'valid') {
        throw new Error(`${BASELINE_ERROR_CODES.INVALID_CONFIG}: ${validationResult.errors.join(', ')}`);
      }

      // ✅ Check for concurrent generation limit
      if (this._activeGenerations.size >= PERFORMANCE_CONSTANTS.MAX_CONCURRENT_BASELINES) {
        throw new Error(`${BASELINE_ERROR_CODES.THRESHOLD_VIOLATION}: Maximum concurrent baseline generations exceeded`);
      }

      // ✅ Mark generation as active
      this._activeGenerations.add(config.baselineId);

      this.logInfo('Starting baseline generation', {
        baselineId: config.baselineId,
        targetComponents: config.targetComponents.length,
        samplingDuration: config.samplingDuration
      });

      // ✅ Generate baseline with timing measurement
      const result = await this._metricsCollector.timer.measure(async () => {
        return await this._performBaselineGeneration(config);
      });

      // ✅ Cache successful result
      this._baselineCache.set(config.baselineId, result.result);

      // ✅ Record timing metrics
      this._metricsCollector.recordTiming('baseline-generation', result.timing);

      this.logInfo('Baseline generation completed', {
        baselineId: config.baselineId,
        duration: result.timing.duration,
        componentsProcessed: result.result.componentResults.length,
        status: result.result.status
      });

      return result.result;

    } catch (error) {
      this.logError('Baseline generation failed', {
        baselineId: config.baselineId,
        error: error instanceof Error ? error.message : String(error)
      });

      // ✅ Create failure result
      const failureResult: TPerformanceBaselineResult = {
        baselineId: config.baselineId,
        name: config.name,
        description: config.description,
        timestamp: new Date().toISOString(),
        environment: config.environment,
        status: 'failed',
        componentResults: [],
        aggregatedMetrics: this._createEmptyMetrics(),
        statistics: this._createFailureStatistics(),
        validationResults: [this._createValidationResult(
          config.baselineId,
          'invalid',
          0,
          [],
          [],
          [error instanceof Error ? error.message : String(error)]
        )],
        metadata: { error: error instanceof Error ? error.message : String(error) }
      };

      return failureResult;

    } finally {
      // ✅ Clean up active generation tracking
      this._activeGenerations.delete(config.baselineId);

      // ✅ End timing measurement
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('baseline-generation-total', timing);
    }
  }

  /**
   * Validate existing baseline against current performance
   *
   * @param baselineId - Identifier of baseline to validate
   * @param currentMetrics - Current performance metrics
   * @returns Promise resolving to validation result
   */
  async validateBaseline(baselineId: string, currentMetrics: TPerformanceMetricsData): Promise<TValidationResult> {
    const timingContext = this._resilientTimer.start();

    try {
      // ✅ Retrieve baseline from cache or storage
      const baseline = this._baselineCache.get(baselineId);
      if (!baseline) {
        return this._createValidationResult(
          baselineId,
          'invalid',
          0,
          [],
          [],
          [`Baseline ${baselineId} not found`]
        );
      }

      // ✅ Perform validation against baseline thresholds
      const validationResult = await this._performBaselineValidation(baseline, currentMetrics);

      this.logInfo('Baseline validation completed', {
        baselineId,
        status: validationResult.status,
        errorCount: validationResult.errors.length
      });

      return validationResult;

    } catch (error) {
      this.logError('Baseline validation failed', {
        baselineId,
        error: error instanceof Error ? error.message : String(error)
      });

      return this._createValidationResult(
        baselineId,
        'invalid',
        0,
        [],
        [],
        [error instanceof Error ? error.message : String(error)]
      );

    } finally {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('baseline-validation', timing);
    }
  }

  /**
   * Update baseline with new performance data
   *
   * @param baselineId - Identifier of baseline to update
   * @param updateData - New performance data
   * @returns Promise resolving to updated baseline result
   */
  async updateBaseline(baselineId: string, updateData: Partial<TPerformanceMetricsData>): Promise<TPerformanceBaselineResult> {
    const timingContext = this._resilientTimer.start();

    try {
      // ✅ Retrieve existing baseline
      const existingBaseline = this._baselineCache.get(baselineId);
      if (!existingBaseline) {
        throw new Error(`${BASELINE_ERROR_CODES.COMPONENT_NOT_FOUND}: Baseline ${baselineId} not found`);
      }

      // ✅ Merge update data with existing baseline
      const updatedBaseline = await this._mergeBaselineData(existingBaseline, updateData);

      // ✅ Update cache
      this._baselineCache.set(baselineId, updatedBaseline);

      this.logInfo('Baseline updated successfully', {
        baselineId,
        updateFields: Object.keys(updateData)
      });

      return updatedBaseline;

    } catch (error) {
      this.logError('Baseline update failed', {
        baselineId,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;

    } finally {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('baseline-update', timing);
    }
  }

  /**
   * Retrieve baseline by identifier
   *
   * @param baselineId - Identifier of baseline to retrieve
   * @returns Promise resolving to baseline result or null if not found
   */
  async getBaseline(baselineId: string): Promise<TPerformanceBaselineResult | null> {
    const timingContext = this._resilientTimer.start();

    try {
      const baseline = this._baselineCache.get(baselineId);

      if (baseline) {
        this.logInfo('Baseline retrieved from cache', { baselineId });
      } else {
        this.logInfo('Baseline not found', { baselineId });
      }

      return baseline || null;

    } finally {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('baseline-retrieval', timing);
    }
  }

  /**
   * Delete baseline by identifier
   *
   * @param baselineId - Identifier of baseline to delete
   * @returns Promise resolving to deletion success status
   */
  async deleteBaseline(baselineId: string): Promise<boolean> {
    const timingContext = this._resilientTimer.start();

    try {
      const existed = this._baselineCache.has(baselineId);
      this._baselineCache.delete(baselineId);
      this._activeGenerations.delete(baselineId);

      this.logInfo('Baseline deletion completed', {
        baselineId,
        existed,
        success: true
      });

      return true;

    } catch (error) {
      this.logError('Baseline deletion failed', {
        baselineId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;

    } finally {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('baseline-deletion', timing);
    }
  }

  // ============================================================================
  // REQUIRED ABSTRACT METHOD IMPLEMENTATIONS
  // AI Context: Required implementations from BaseTrackingService
  // ============================================================================

  /**
   * Get service name - required by BaseTrackingService
   */
  protected getServiceName(): string {
    return 'baseline-generator-core';
  }

  /**
   * Get service version - required by BaseTrackingService
   */
  protected getServiceVersion(): string {
    return '1.0.0';
  }

  /**
   * Perform service-specific tracking - required by BaseTrackingService
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    // Track baseline generation operations
    this.logInfo('Tracking baseline operation', {
      componentId: data.componentId,
      status: data.status,
      timestamp: data.timestamp,
      context: data.context.contextId
    });
  }

  /**
   * Perform service-specific validation - required by BaseTrackingService
   */
  protected async doValidate(): Promise<TValidationResult> {
    const validationId = crypto.randomUUID();

    return this._createValidationResult(
      validationId,
      'valid',
      100,
      [],
      [],
      []
    );
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // AI Context: Internal helper methods for baseline operations
  // ============================================================================

  /**
   * Validate baseline configuration
   */
  private async _validateBaselineConfig(config: TPerformanceBaselineConfig): Promise<TValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // ✅ Merge with default configuration to ensure all required fields
    const mergedConfig = { ...DEFAULT_BASELINE_CONFIG, ...config };

    // Validate required fields
    if (!mergedConfig.baselineId) {
      errors.push('baselineId is required');
    }
    if (!mergedConfig.name) {
      errors.push('name is required');
    }
    if (!mergedConfig.targetComponents || mergedConfig.targetComponents.length === 0) {
      errors.push('targetComponents must contain at least one component');
    }

    // Validate sampling parameters
    if (mergedConfig.samplingInterval <= 0) {
      errors.push('samplingInterval must be positive');
    }
    if (mergedConfig.samplingDuration <= 0) {
      errors.push('samplingDuration must be positive');
    }

    // Validate thresholds
    if (mergedConfig.thresholds?.responseTime <= 0) {
      errors.push('responseTime threshold must be positive');
    }

    // Check for warnings
    if (mergedConfig.samplingDuration < 30000) {
      warnings.push('samplingDuration less than 30 seconds may not provide reliable results');
    }
    if (mergedConfig.thresholds?.responseTime > 100) {
      warnings.push('responseTime threshold greater than 100ms may be too lenient for Enhanced components');
    }

    return this._createValidationResult(
      config.baselineId,
      errors.length === 0 ? 'valid' : 'invalid',
      errors.length === 0 ? 100 : 0,
      [],
      warnings,
      errors
    );
  }

  /**
   * Perform actual baseline generation
   */
  private async _performBaselineGeneration(config: TPerformanceBaselineConfig): Promise<TPerformanceBaselineResult> {
    const startTime = new Date().toISOString();
    const componentResults: TComponentBaselineResult[] = [];

    // Generate baseline for each target component
    for (const componentId of config.targetComponents) {
      try {
        const componentResult = await this._generateComponentBaseline(componentId, config);
        componentResults.push(componentResult);
      } catch (error) {
        // Create failed component result
        componentResults.push({
          componentId,
          componentName: componentId,
          componentType: 'unknown',
          status: 'failed',
          metrics: this._createEmptyMetrics(),
          analysis: this._createEmptyAnalysis(),
          thresholdValidation: [],
          recommendations: [`Failed to generate baseline: ${error instanceof Error ? error.message : String(error)}`],
          metadata: { error: error instanceof Error ? error.message : String(error) }
        });
      }
    }

    // Calculate aggregated metrics
    const aggregatedMetrics = this._aggregateComponentMetrics(componentResults);

    // Generate statistics
    const statistics = this._generateBaselineStatistics(componentResults, startTime);

    // Validate against thresholds
    const validationResults = await this._validateAgainstThresholds(aggregatedMetrics, config.thresholds);

    return {
      baselineId: config.baselineId,
      name: config.name,
      description: config.description,
      timestamp: startTime,
      environment: config.environment,
      status: componentResults.every(r => r.status === 'success') ? 'success' :
              componentResults.some(r => r.status === 'success') ? 'partial' : 'failed',
      componentResults,
      aggregatedMetrics,
      statistics,
      validationResults,
      metadata: config.metadata
    };
  }

  /**
   * Create validation result with proper TValidationResult structure
   */
  private _createValidationResult(
    validationId: string,
    status: 'valid' | 'invalid',
    score: number,
    checks: any[],
    warnings: string[],
    errors: string[]
  ): TValidationResult {
    return {
      validationId,
      componentId: this.getServiceName(),
      timestamp: new Date(),
      executionTime: 0,
      status,
      overallScore: score,
      checks,
      references: {
        componentId: this.getServiceName(),
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings,
      errors,
      metadata: {
        validationMethod: 'baseline-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  /**
   * Perform baseline validation against current metrics
   */
  private async _performBaselineValidation(
    baseline: TPerformanceBaselineResult,
    currentMetrics: TPerformanceMetricsData
  ): Promise<TValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Compare current metrics against baseline thresholds
    if (baseline.aggregatedMetrics && currentMetrics.responseTime.average > baseline.aggregatedMetrics.responseTime.average * 1.2) {
      errors.push('Response time exceeds baseline by more than 20%');
    }

    if (baseline.aggregatedMetrics && currentMetrics.memoryUsage.average > baseline.aggregatedMetrics.memoryUsage.average * 1.1) {
      warnings.push('Memory usage exceeds baseline by more than 10%');
    }

    return this._createValidationResult(
      baseline.baselineId,
      errors.length === 0 ? 'valid' : 'invalid',
      errors.length === 0 ? 100 : Math.max(0, 100 - (errors.length * 20)),
      [],
      warnings,
      errors
    );
  }

  /**
   * Merge baseline data with update data
   */
  private async _mergeBaselineData(
    existingBaseline: TPerformanceBaselineResult,
    updateData: Partial<TPerformanceMetricsData>
  ): Promise<TPerformanceBaselineResult> {
    // Create updated baseline with merged metrics
    const baseMetrics = existingBaseline.aggregatedMetrics || {
      timestamp: new Date().toISOString(),
      componentId: existingBaseline.baselineId,
      duration: 0,
      responseTime: { average: 0, median: 0, min: 0, max: 0, p95: 0, p99: 0, standardDeviation: 0, sampleCount: 0 },
      memoryUsage: { average: 0, peak: 0, minimum: 0 },
      cpuUsage: { average: 0, peak: 0, minimum: 0 },
      throughput: { average: 0, peak: 0, minimum: 0 },
      errorRate: { average: 0, peak: 0, trend: 'stable' },
      networkLatency: { average: 0, peak: 0, minimum: 0 },
      diskIO: { read: 0, write: 0, total: 0 },
      cacheHitRate: { percentage: 0, hits: 0, misses: 0 }
    };

    const updatedBaseline: TPerformanceBaselineResult = {
      ...existingBaseline,
      timestamp: new Date().toISOString(),
      aggregatedMetrics: {
        ...baseMetrics,
        ...updateData,
        timestamp: updateData.timestamp || baseMetrics.timestamp
      } as TPerformanceMetricsData
    };

    return updatedBaseline;
  }

  /**
   * Generate component baseline
   */
  private async _generateComponentBaseline(
    componentId: string,
    config: TPerformanceBaselineConfig
  ): Promise<TComponentBaselineResult> {
    // Simulate component baseline generation
    const metrics = this._createMockMetrics();
    const analysis = this._createEmptyAnalysis();
    const thresholdValidation = await this._validateComponentThresholds(metrics, config.thresholds);

    return {
      componentId,
      componentName: componentId,
      componentType: 'component',
      status: 'success',
      metrics,
      analysis,
      thresholdValidation,
      recommendations: [],
      metadata: {}
    };
  }

  /**
   * Cleanup expired baselines
   */
  private _cleanupExpiredBaselines(): void {
    const now = Date.now();
    const expiredBaselines: string[] = [];

    for (const [baselineId, baseline] of Array.from(this._baselineCache.entries())) {
      const baselineAge = now - new Date(baseline.timestamp).getTime();
      if (baselineAge > PERFORMANCE_CONSTANTS.MAX_BASELINE_AGE) {
        expiredBaselines.push(baselineId);
      }
    }

    // Remove expired baselines
    for (const baselineId of expiredBaselines) {
      this._baselineCache.delete(baselineId);
    }

    if (expiredBaselines.length > 0) {
      this.logInfo('Cleaned up expired baselines', {
        count: expiredBaselines.length,
        baselineIds: expiredBaselines
      });
    }
  }

  /**
   * Create mock metrics for testing/simulation
   */
  private _createMockMetrics(): TPerformanceMetricsData {
    return {
      timestamp: new Date().toISOString(),
      duration: 60000,
      responseTime: {
        average: 5,
        median: 4,
        min: 1,
        max: 15,
        p95: 10,
        p99: 12,
        standardDeviation: 2.5,
        sampleCount: 1000
      },
      memoryUsage: {
        average: 25 * 1024 * 1024,
        peak: 40 * 1024 * 1024,
        minimum: 20 * 1024 * 1024,
        growthRate: 0,
        leakIndicators: [],
        gcStatistics: {
          totalCycles: 10,
          averageDuration: 5,
          memoryFreed: 5 * 1024 * 1024,
          efficiency: 95
        }
      },
      cpuUsage: {
        average: 10,
        peak: 25,
        minimum: 5,
        distribution: [5, 8, 10, 12, 15],
        efficiencyScore: 85
      },
      throughput: {
        operationsPerSecond: 1200,
        requestsPerSecond: 800,
        dataThroughput: 1024 * 1024,
        peakThroughput: 1500,
        efficiency: 90
      },
      errorRate: {
        overall: 0.1,
        byType: { 'timeout': 0.05, 'validation': 0.05 },
        bySeverity: { 'warning': 0.08, 'error': 0.02 },
        trend: 'stable'
      },
      networkIO: {
        bytesReceived: 1024 * 512,
        bytesSent: 1024 * 256,
        latency: 2,
        connectionCount: 10
      },
      diskIO: {
        bytesRead: 1024 * 100,
        bytesWritten: 1024 * 50,
        latency: 1,
        iops: 500
      },
      customMetrics: {},
      reliabilityScore: 95,
      metadata: {}
    };
  }

  /**
   * Create empty metrics for failed operations
   */
  private _createEmptyMetrics(): TPerformanceMetricsData {
    return {
      timestamp: new Date().toISOString(),
      duration: 0,
      responseTime: {
        average: 0, median: 0, min: 0, max: 0, p95: 0, p99: 0,
        standardDeviation: 0, sampleCount: 0
      },
      memoryUsage: {
        average: 0, peak: 0, minimum: 0, growthRate: 0,
        leakIndicators: [],
        gcStatistics: { totalCycles: 0, averageDuration: 0, memoryFreed: 0, efficiency: 0 }
      },
      cpuUsage: {
        average: 0, peak: 0, minimum: 0, distribution: [], efficiencyScore: 0
      },
      throughput: {
        operationsPerSecond: 0, requestsPerSecond: 0, dataThroughput: 0,
        peakThroughput: 0, efficiency: 0
      },
      errorRate: {
        overall: 100, byType: {}, bySeverity: {}, trend: 'stable'
      },
      networkIO: {
        bytesReceived: 0, bytesSent: 0, latency: 0, connectionCount: 0
      },
      diskIO: {
        bytesRead: 0, bytesWritten: 0, latency: 0, iops: 0
      },
      customMetrics: {},
      reliabilityScore: 0,
      metadata: {}
    };
  }

  /**
   * Create empty analysis for failed operations
   */
  private _createEmptyAnalysis(): TPerformanceAnalysis {
    return {
      performanceScore: 0,
      trend: 'stable',
      bottlenecks: [],
      recommendations: [],
      baselineComparison: {
        status: 'worse',
        deltaPercentage: -100,
        metricComparisons: {},
        assessment: 'Failed to generate analysis'
      }
    };
  }

  /**
   * Validate component against thresholds
   */
  private async _validateComponentThresholds(
    metrics: TPerformanceMetricsData,
    thresholds: TPerformanceThresholds
  ): Promise<TThresholdValidationResult[]> {
    const results: TThresholdValidationResult[] = [];

    // Validate response time
    results.push({
      metricName: 'responseTime',
      thresholdValue: thresholds.responseTime,
      actualValue: metrics.responseTime.average,
      status: metrics.responseTime.average <= thresholds.responseTime ? 'passed' : 'failed',
      severity: metrics.responseTime.average > thresholds.responseTime * 2 ? 'critical' : 'medium',
      message: `Response time: ${metrics.responseTime.average}ms (threshold: ${thresholds.responseTime}ms)`
    });

    // Validate memory usage
    results.push({
      metricName: 'memoryUsage',
      thresholdValue: thresholds.memoryUsage,
      actualValue: metrics.memoryUsage.average,
      status: metrics.memoryUsage.average <= thresholds.memoryUsage ? 'passed' : 'failed',
      severity: metrics.memoryUsage.average > thresholds.memoryUsage * 2 ? 'critical' : 'medium',
      message: `Memory usage: ${Math.round(metrics.memoryUsage.average / 1024 / 1024)}MB (threshold: ${Math.round(thresholds.memoryUsage / 1024 / 1024)}MB)`
    });

    return results;
  }

  /**
   * Aggregate component metrics
   */
  private _aggregateComponentMetrics(componentResults: TComponentBaselineResult[]): TPerformanceMetricsData {
    if (componentResults.length === 0) {
      return this._createEmptyMetrics();
    }

    const successfulResults = componentResults.filter(r => r.status === 'success');
    if (successfulResults.length === 0) {
      return this._createEmptyMetrics();
    }

    // Calculate averages across all successful components
    const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.metrics.responseTime.average, 0) / successfulResults.length;
    const avgMemoryUsage = successfulResults.reduce((sum, r) => sum + r.metrics.memoryUsage.average, 0) / successfulResults.length;
    const avgCpuUsage = successfulResults.reduce((sum, r) => sum + r.metrics.cpuUsage.average, 0) / successfulResults.length;

    // Use first successful result as template and update with aggregated values
    const template = successfulResults[0].metrics;
    return {
      ...template,
      responseTime: {
        ...template.responseTime,
        average: avgResponseTime
      },
      memoryUsage: {
        ...template.memoryUsage,
        average: avgMemoryUsage
      },
      cpuUsage: {
        ...template.cpuUsage,
        average: avgCpuUsage
      }
    };
  }

  /**
   * Generate baseline statistics
   */
  private _generateBaselineStatistics(componentResults: TComponentBaselineResult[], startTime: string): TBaselineStatistics {
    const endTime = new Date().toISOString();
    const generationDuration = new Date(endTime).getTime() - new Date(startTime).getTime();

    return {
      totalComponents: componentResults.length,
      successfulComponents: componentResults.filter(r => r.status === 'success').length,
      failedComponents: componentResults.filter(r => r.status === 'failed').length,
      totalSamples: componentResults.reduce((sum, r) => sum + (r.metrics.responseTime.sampleCount || 0), 0),
      generationDuration,
      dataQualityScore: componentResults.length > 0 ?
        (componentResults.filter(r => r.status === 'success').length / componentResults.length) * 100 : 0
    };
  }

  /**
   * Validate against thresholds
   */
  private async _validateAgainstThresholds(
    metrics: TPerformanceMetricsData,
    thresholds: TPerformanceThresholds
  ): Promise<TValidationResult[]> {
    const validationResult = await this._validateComponentThresholds(metrics, thresholds);

    const hasFailures = validationResult.some(r => r.status === 'failed');

    return [this._createValidationResult(
      'threshold-validation',
      hasFailures ? 'invalid' : 'valid',
      hasFailures ? 50 : 100,
      validationResult,
      validationResult.filter(r => r.severity === 'medium').map(r => r.message),
      validationResult.filter(r => r.status === 'failed').map(r => r.message)
    )];
  }

  /**
   * Create failure statistics
   */
  private _createFailureStatistics(): TBaselineStatistics {
    return {
      totalComponents: 0,
      successfulComponents: 0,
      failedComponents: 0,
      totalSamples: 0,
      generationDuration: 0,
      dataQualityScore: 0
    };
  }
}
