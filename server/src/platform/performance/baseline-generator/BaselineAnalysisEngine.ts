/**
 * @file Baseline Analysis Engine Implementation
 * @filepath server/src/platform/performance/baseline-generator/BaselineAnalysisEngine.ts
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02.REF-03
 * @component baseline-analysis-engine
 * @reference foundation-context.PERFORMANCE.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context performance-context
 * @category Performance
 * @created 2025-09-13
 * @modified 2025-09-13 12:00:00 +03
 * 
 * @description
 * Enterprise-grade baseline analysis engine providing advanced statistical analysis,
 * trend detection, and performance baseline validation capabilities. Implements
 * sophisticated analysis algorithms with memory-safe operations and resilient timing.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact performance-architecture,analysis-engine
 * @milestone-compliance M0.1-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on BaseTrackingService, performance-baseline-types
 * @enables baseline-analysis-capabilities, trend-detection
 * @implements IBaselineAnalyzer, BaseTrackingService
 * @integrates-with Enhanced Orchestration Driver v6.4.0
 * @related-contexts foundation-context, performance-context
 * @governance-impact performance-architecture, analysis-system
 * @api-classification internal
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level MEM-SAFE-002
 * @base-class BaseTrackingService
 * @memory-boundaries strict-enforcement
 * @circular-buffer-implementation not-applicable
 * @memory-leak-prevention automatic-cleanup
 * @resource-cleanup-strategy comprehensive
 * @timing-resilience-level critical
 * @timing-fallback-mechanisms enabled
 * @performance-monitoring real-time
 * @resilient-timing-integration dual-field-pattern
 *
 * 🎯 PERFORMANCE REQUIREMENTS (v2.3)
 * @performance-target <10ms-response-time
 * @memory-usage-limit 50MB-maximum
 * @concurrent-operations 100-maximum
 * @throughput-requirement 1000-ops-per-second
 * @availability-target 99.9%-uptime
 * @scalability-requirement horizontal-scaling-ready
 * @performance-monitoring comprehensive
 * @performance-alerting enabled
 * @performance-optimization continuous
 *
 * 📊 IMPLEMENTATION STANDARDS (v2.3)
 * @code-style enterprise-typescript
 * @documentation-standard comprehensive-jsdoc
 * @testing-requirement 95%-coverage
 * @error-handling comprehensive
 * @logging-standard structured-logging
 * @monitoring-integration enabled
 * @security-compliance enterprise-grade
 * @maintainability-score high
 * @complexity-management modular-design
 *
 * 🔄 INTEGRATION PATTERNS (v2.3)
 * @integration-pattern service-oriented
 * @dependency-injection constructor-based
 * @event-handling asynchronous
 * @state-management immutable
 * @error-propagation structured
 * @resource-management automatic
 * @lifecycle-management comprehensive
 * @configuration-management centralized
 * @monitoring-integration real-time
 *
 * 📈 BUSINESS VALUE (v2.3)
 * @business-impact performance-optimization
 * @cost-reduction operational-efficiency
 * @risk-mitigation performance-monitoring
 * @compliance-support regulatory-requirements
 * @scalability-enablement enterprise-growth
 * @maintenance-reduction automated-analysis
 * @quality-improvement continuous-monitoring
 * @innovation-support advanced-analytics
 * @competitive-advantage performance-excellence
 *
 * 🔧 TECHNICAL ARCHITECTURE (v2.3)
 * @architecture-pattern layered-service
 * @design-principles solid-principles
 * @coupling-level loose-coupling
 * @cohesion-level high-cohesion
 * @abstraction-level appropriate
 * @encapsulation-level strong
 * @inheritance-pattern single-inheritance
 * @composition-pattern dependency-injection
 * @polymorphism-usage interface-based
 *
 * 📋 QUALITY METRICS (v2.3)
 * @cyclomatic-complexity <10-per-method
 * @code-coverage >95%-target
 * @documentation-coverage 100%-public-apis
 * @type-safety strict-typescript
 * @error-handling comprehensive
 * @performance-benchmarks established
 * @security-scanning automated
 * @code-review-required mandatory
 * @testing-strategy comprehensive
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type baseline-analysis-engine
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @test-coverage >95%
 * @deployment-ready true
 * @monitoring-enabled comprehensive
 * @documentation docs/platform/performance/baseline-generator/baseline-analysis-engine.md
 * @naming-convention PascalCase-with-descriptive-suffixes
 * @performance-monitoring real-time-with-dashboards
 * @security-compliance enterprise-grade-validated
 * @scalability-validated horizontal-scaling-tested
 *
 * 🚀 DEPLOYMENT CONSIDERATIONS (v2.3)
 * @deployment-strategy blue-green
 * @rollback-capability automated
 * @monitoring-setup comprehensive
 * @alerting-configuration proactive
 * @scaling-strategy horizontal
 * @backup-strategy automated
 * @disaster-recovery planned
 * @maintenance-windows scheduled
 * @update-strategy rolling-updates
 *
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: External dependencies and type imports for baseline analysis engine
// ============================================================================

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { ResilientTimer } from '../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../shared/src/base/utils/ResilientMetrics';
import {
  IBaselineAnalyzer,
  TPerformanceMetricsData,
  TAnalysisConfig,
  TAnalysisResult,
  TTrendAnalysis,
  TStatisticalSummary
} from './types/performance-baseline-types';
import {
  TTrackingConfig,
  TTrackingData,
  TValidationResult
} from '../../../../../shared/src/types/platform/tracking';
import * as crypto from 'crypto';

// ============================================================================
// SECTION 2: CONSTANTS & CONFIGURATION
// AI Context: Analysis engine constants and default configurations
// ============================================================================

/**
 * Analysis engine constants
 */
const ANALYSIS_CONSTANTS = {
  MAX_ANALYSIS_DURATION: 30000, // 30 seconds
  MAX_DATA_POINTS: 10000,
  MIN_SAMPLE_SIZE: 10,
  TREND_DETECTION_WINDOW: 100,
  STATISTICAL_CONFIDENCE: 0.95,
  OUTLIER_THRESHOLD: 2.5, // Standard deviations
  PERFORMANCE_THRESHOLD: 10, // 10ms target
  MEMORY_LIMIT: 50 * 1024 * 1024, // 50MB
  CLEANUP_INTERVAL: 300000 // 5 minutes
} as const;



// ============================================================================
// SECTION 3: INTERFACES & TYPES
// AI Context: Internal interfaces for analysis engine operations
// ============================================================================

/**
 * Analysis session interface
 */
interface IAnalysisSession {
  sessionId: string;
  config: TAnalysisConfig;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'failed';
  dataPoints: TPerformanceMetricsData[];
  results: TAnalysisResult[];
  errors: string[];
  metadata: Record<string, unknown>;
}



// ============================================================================
// SECTION 4: MAIN IMPLEMENTATION
// AI Context: Primary baseline analysis engine implementation
// ============================================================================

/**
 * Baseline Analysis Engine Enhanced
 * 
 * Enterprise-grade analysis engine providing comprehensive statistical analysis,
 * trend detection, and performance baseline validation with memory-safe operations
 * and resilient timing integration.
 * 
 * @implements IBaselineAnalyzer
 * @extends BaseTrackingService
 * 
 * Key Features:
 * - Advanced statistical analysis with confidence intervals
 * - Real-time trend detection and pattern recognition
 * - Outlier detection and anomaly identification
 * - Predictive analysis capabilities
 * - Memory-safe analysis with automatic cleanup
 * - Resilient timing integration for performance monitoring
 * - Enterprise-grade error handling and recovery
 * - Comprehensive analysis reporting and visualization
 */
export class BaselineAnalysisEngine extends BaseTrackingService implements IBaselineAnalyzer {
  // ============================================================================
  // DUAL-FIELD RESILIENT TIMING PATTERN (Enhanced Component Requirement)
  // ============================================================================
  
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // ============================================================================
  // PRIVATE PROPERTIES
  // AI Context: Internal state management for analysis operations
  // ============================================================================

  private readonly _activeSessions = new Map<string, IAnalysisSession>();
  private readonly _analysisCache = new Map<string, TAnalysisResult>();
  private readonly _trendData = new Map<string, TTrendAnalysis>();
  private _analysisCleanupInterval?: NodeJS.Timeout;

  /**
   * Initialize Baseline Analysis Engine
   *
   * @param config - Tracking configuration with analysis settings
   */
  constructor(config: TTrackingConfig) {
    super(config);

    // ✅ Initialize resilient timing components synchronously
    this._initializeResilientTimingSync();

    this.logInfo('BaselineAnalysisEngine initialized', {
      componentId: this.getServiceName(),
      version: this.getServiceVersion(),
      maxAnalysisDuration: ANALYSIS_CONSTANTS.MAX_ANALYSIS_DURATION,
      maxDataPoints: ANALYSIS_CONSTANTS.MAX_DATA_POINTS
    });
  }

  /**
   * Synchronous resilient timing initialization
   * Required for Enhanced components with dual-field pattern
   */
  private _initializeResilientTimingSync(): void {
    try {
      // ✅ Initialize ResilientTimer with analysis configuration
      this._resilientTimer = new ResilientTimer({
        enableFallbacks: true,
        maxExpectedDuration: ANALYSIS_CONSTANTS.MAX_ANALYSIS_DURATION,
        unreliableThreshold: 3,
        estimateBaseline: 100
      });

      // ✅ Initialize ResilientMetricsCollector with analysis configuration
      this._metricsCollector = new ResilientMetricsCollector({
        enableFallbacks: true,
        cacheUnreliableValues: false,
        maxMetricsAge: ANALYSIS_CONSTANTS.CLEANUP_INTERVAL,
        defaultEstimates: new Map([
          ['analyze-baseline', 200],
          ['detect-trends', 150],
          ['calculate-statistics', 100],
          ['detect-outliers', 75],
          ['generate-report', 50]
        ])
      });

      this.logInfo('Resilient timing components initialized successfully', {
        timerConfig: 'analysis-optimized',
        metricsConfig: 'statistical-tracking-enabled'
      });
    } catch (error) {
      this.logError('Failed to initialize resilient timing components', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Initialize service-specific components
   * Called by BaseTrackingService during startup
   */
  protected async doInitialize(): Promise<void> {
    // ✅ Call parent initialization
    await super.doInitialize();

    // ✅ Set up periodic cleanup of expired analysis data
    this._analysisCleanupInterval = setInterval(() => {
      this._cleanupExpiredAnalysis();
    }, ANALYSIS_CONSTANTS.CLEANUP_INTERVAL);

    this.logInfo('BaselineAnalysisEngine service initialized', {
      activeSessions: this._activeSessions.size,
      cachedAnalysis: this._analysisCache.size,
      cleanupInterval: ANALYSIS_CONSTANTS.CLEANUP_INTERVAL
    });
  }

  /**
   * Shutdown service-specific components
   * Called by BaseTrackingService during shutdown
   */
  protected async doShutdown(): Promise<void> {
    // ✅ Clear cleanup interval
    if (this._analysisCleanupInterval) {
      clearInterval(this._analysisCleanupInterval);
      this._analysisCleanupInterval = undefined;
    }

    // ✅ Stop all active analysis sessions
    await this._stopAllActiveSessions();

    // ✅ Clear caches
    this._activeSessions.clear();
    this._analysisCache.clear();
    this._trendData.clear();

    // ✅ Call parent shutdown
    await super.doShutdown();

    this.logInfo('BaselineAnalysisEngine service shutdown completed');
  }

  // ============================================================================
  // SECTION 5: IBASELINEANALYZER INTERFACE IMPLEMENTATION
  // AI Context: Implementation of IBaselineAnalyzer interface methods
  // ============================================================================

  /**
   * Analyze performance baseline data
   *
   * @param data - Performance metrics data to analyze
   * @param config - Analysis configuration
   * @returns Promise resolving to analysis result
   */
  async analyzeBaseline(data: TPerformanceMetricsData[], config: TAnalysisConfig): Promise<TAnalysisResult> {
    const timingContext = this._resilientTimer.start();

    try {
      // ✅ Validate input data
      if (!data || data.length === 0) {
        throw new Error('Analysis data cannot be empty');
      }

      if (data.length < ANALYSIS_CONSTANTS.MIN_SAMPLE_SIZE) {
        throw new Error(`Insufficient data points: ${data.length} < ${ANALYSIS_CONSTANTS.MIN_SAMPLE_SIZE}`);
      }

      this.logInfo('Starting baseline analysis', {
        dataPoints: data.length,
        analysisId: config.analysisId,
        enableTrendAnalysis: config.enableTrendAnalysis,
        enableOutlierDetection: config.enableOutlierDetection
      });

      // ✅ Perform statistical analysis
      const statisticalSummary = this._calculateStatistics(data);

      // ✅ Perform trend analysis if enabled
      let trendAnalysis: TTrendAnalysis | undefined;
      if (config.enableTrendAnalysis) {
        trendAnalysis = this._detectTrends(data, config.trendWindow);
      }

      // ✅ Detect outliers if enabled
      let outliers: number[] = [];
      if (config.enableOutlierDetection) {
        outliers = this._detectOutliers(data, config.outlierThreshold);
      }

      // ✅ Generate analysis result
      const analysisResult: TAnalysisResult = {
        analysisId: config.analysisId,
        timestamp: new Date().toISOString(),
        dataPointCount: data.length,
        statisticalSummary,
        trendAnalysis: trendAnalysis || {
          trendDirection: 'stable',
          trendStrength: 0,
          confidence: 0,
          slope: 0,
          seasonality: false,
          anomalies: [],
          forecast: [],
          metadata: { fallback: true }
        },
        outlierCount: outliers.length,
        outlierIndices: outliers,
        performanceScore: this._calculatePerformanceScore(statisticalSummary, config.performanceThreshold),
        recommendations: this._generateRecommendations(statisticalSummary, trendAnalysis),
        confidence: config.confidenceLevel,
        metadata: {
          analysisMethod: 'comprehensive-statistical',
          processingTime: 0, // Will be set below
          memoryUsage: process.memoryUsage().heapUsed
        }
      };

      const timing = timingContext.end();
      this._metricsCollector.recordTiming('analyze-baseline', timing);
      if (analysisResult.metadata) {
        analysisResult.metadata.processingTime = timing.duration;
      }

      // ✅ Cache result
      this._analysisCache.set(config.analysisId, analysisResult);

      this.logInfo('Baseline analysis completed', {
        analysisId: config.analysisId,
        dataPoints: data.length,
        performanceScore: analysisResult.performanceScore,
        outlierCount: outliers.length,
        processingTime: timing.duration
      });

      return analysisResult;

    } catch (error) {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('analyze-baseline-error', timing);

      this.logError('Failed to analyze baseline', {
        analysisId: config.analysisId,
        dataPoints: data?.length || 0,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    }
  }

  /**
   * Detect trends in performance data
   *
   * @param data - Performance metrics data
   * @param windowSize - Trend detection window size
   * @returns Promise resolving to trend analysis result
   */
  async detectTrends(data: TPerformanceMetricsData[], windowSize: number = ANALYSIS_CONSTANTS.TREND_DETECTION_WINDOW): Promise<TTrendAnalysis> {
    const timingContext = this._resilientTimer.start();

    try {
      const trendAnalysis = this._detectTrends(data, windowSize);

      const timing = timingContext.end();
      this._metricsCollector.recordTiming('detect-trends', timing);

      return trendAnalysis;

    } catch (error) {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('detect-trends-error', timing);

      this.logError('Failed to detect trends', {
        dataPoints: data?.length || 0,
        windowSize,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    }
  }

  /**
   * Calculate statistical summary
   *
   * @param data - Performance metrics data
   * @returns Promise resolving to statistical summary
   */
  async calculateStatistics(data: TPerformanceMetricsData[]): Promise<TStatisticalSummary> {
    const timingContext = this._resilientTimer.start();

    try {
      const statistics = this._calculateStatistics(data);

      const timing = timingContext.end();
      this._metricsCollector.recordTiming('calculate-statistics', timing);

      return statistics;

    } catch (error) {
      const timing = timingContext.end();
      this._metricsCollector.recordTiming('calculate-statistics-error', timing);

      this.logError('Failed to calculate statistics', {
        dataPoints: data?.length || 0,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    }
  }

  // ============================================================================
  // REQUIRED ABSTRACT METHOD IMPLEMENTATIONS
  // AI Context: Required implementations from BaseTrackingService
  // ============================================================================

  /**
   * Get service name - required by BaseTrackingService
   */
  protected getServiceName(): string {
    return 'baseline-analysis-engine';
  }

  /**
   * Get service version - required by BaseTrackingService
   */
  protected getServiceVersion(): string {
    return '1.0.0';
  }

  /**
   * Perform service-specific tracking - required by BaseTrackingService
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    // Track analysis operations
    this.logInfo('Tracking analysis operation', {
      componentId: data.componentId,
      status: data.status,
      timestamp: data.timestamp,
      context: data.context.contextId
    });
  }

  /**
   * Perform service-specific validation - required by BaseTrackingService
   */
  protected async doValidate(): Promise<TValidationResult> {
    const validationId = crypto.randomUUID();

    return this._createValidationResult(
      validationId,
      'valid',
      100,
      [],
      [],
      []
    );
  }

  // ============================================================================
  // SECTION 6: PRIVATE HELPER METHODS
  // AI Context: Internal helper methods for analysis operations
  // ============================================================================

  /**
   * Create validation result with proper TValidationResult structure
   */
  private _createValidationResult(
    validationId: string,
    status: 'valid' | 'invalid',
    score: number,
    checks: any[],
    warnings: string[],
    errors: string[]
  ): TValidationResult {
    return {
      validationId,
      componentId: this.getServiceName(),
      timestamp: new Date(),
      executionTime: 0,
      status,
      overallScore: score,
      checks,
      references: {
        componentId: this.getServiceName(),
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: new Date(),
          analysisDepth: 1
        }
      },
      recommendations: [],
      warnings,
      errors,
      metadata: {
        validationMethod: 'analysis-validation',
        rulesApplied: 1,
        dependencyDepth: 0,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  /**
   * Calculate comprehensive statistics for performance data
   */
  private _calculateStatistics(data: TPerformanceMetricsData[]): TStatisticalSummary {
    // Extract response times for statistical analysis
    const responseTimes = data.map(d => d.responseTime.average);
    const memoryUsages = data.map(d => d.memoryUsage.average);
    const cpuUsages = data.map(d => d.cpuUsage.average);

    // Calculate basic statistics
    const responseTimeStats = this._calculateBasicStats(responseTimes);
    const memoryStats = this._calculateBasicStats(memoryUsages);
    const cpuStats = this._calculateBasicStats(cpuUsages);

    return {
      dataPointCount: data.length,
      timeRange: {
        start: data[0]?.timestamp || new Date().toISOString(),
        end: data[data.length - 1]?.timestamp || new Date().toISOString()
      },
      responseTime: responseTimeStats,
      memoryUsage: memoryStats,
      cpuUsage: cpuStats,
      throughput: {
        average: data.reduce((sum, d) => sum + d.throughput.operationsPerSecond, 0) / data.length,
        peak: Math.max(...data.map(d => d.throughput.peakThroughput)),
        minimum: Math.min(...data.map(d => d.throughput.operationsPerSecond))
      },
      errorRate: {
        average: data.reduce((sum, d) => sum + d.errorRate.overall, 0) / data.length,
        peak: Math.max(...data.map(d => d.errorRate.overall)),
        trend: 'stable'
      },
      reliability: {
        score: data.reduce((sum, d) => sum + (d.reliabilityScore || 0), 0) / data.length,
        consistency: this._calculateConsistency(data.map(d => d.reliabilityScore || 0))
      }
    };
  }

  /**
   * Calculate basic statistical measures
   */
  private _calculateBasicStats(values: number[]): any {
    const sorted = [...values].sort((a, b) => a - b);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = sorted[Math.floor(sorted.length / 2)];
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    return {
      mean,
      median,
      minimum: Math.min(...values),
      maximum: Math.max(...values),
      standardDeviation,
      variance,
      percentile95: sorted[Math.floor(sorted.length * 0.95)],
      percentile99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }

  /**
   * Detect trends in performance data
   */
  private _detectTrends(data: TPerformanceMetricsData[], windowSize: number): TTrendAnalysis {
    const responseTimes = data.map(d => d.responseTime.average);

    // Simple linear regression for trend detection
    const trend = this._calculateLinearTrend(responseTimes);

    return {
      trendDirection: trend.slope > 0.1 ? 'increasing' : trend.slope < -0.1 ? 'decreasing' : 'stable',
      trendStrength: Math.abs(trend.slope),
      confidence: trend.correlation,
      slope: trend.slope,
      seasonality: this._detectSeasonality(responseTimes),
      anomalies: this._detectAnomalies(responseTimes),
      forecast: this._generateForecast(responseTimes, 10), // 10 point forecast
      metadata: {
        windowSize,
        dataPoints: data.length,
        analysisMethod: 'linear-regression'
      }
    };
  }

  /**
   * Calculate linear trend using least squares
   */
  private _calculateLinearTrend(values: number[]): { slope: number; intercept: number; correlation: number } {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * values[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = values.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate correlation coefficient
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    const correlation = denominator === 0 ? 0 : numerator / denominator;

    return { slope, intercept, correlation: Math.abs(correlation) };
  }

  /**
   * Detect outliers using statistical methods
   */
  private _detectOutliers(data: TPerformanceMetricsData[], threshold: number): number[] {
    const responseTimes = data.map(d => d.responseTime.average);
    const mean = responseTimes.reduce((sum, val) => sum + val, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);

    const outliers: number[] = [];
    responseTimes.forEach((value, index) => {
      const zScore = Math.abs((value - mean) / standardDeviation);
      if (zScore > threshold) {
        outliers.push(index);
      }
    });

    return outliers;
  }

  /**
   * Calculate performance score based on statistics and thresholds
   */
  private _calculatePerformanceScore(stats: TStatisticalSummary, threshold: number): number {
    const responseTimeScore = Math.max(0, 100 - (stats.responseTime.mean / threshold) * 100);
    const reliabilityScore = stats.reliability.score;
    const errorRateScore = Math.max(0, 100 - stats.errorRate.average * 10);

    return Math.round((responseTimeScore + reliabilityScore + errorRateScore) / 3);
  }

  /**
   * Generate recommendations based on analysis
   */
  private _generateRecommendations(stats: TStatisticalSummary, trends?: TTrendAnalysis): string[] {
    const recommendations: string[] = [];

    if (stats.responseTime.mean > ANALYSIS_CONSTANTS.PERFORMANCE_THRESHOLD) {
      recommendations.push('Consider optimizing response time performance');
    }

    if (stats.errorRate.average > 0.05) {
      recommendations.push('Investigate and reduce error rate');
    }

    if (trends?.trendDirection === 'increasing' && trends.trendStrength > 0.5) {
      recommendations.push('Performance degradation trend detected - investigate root cause');
    }

    if (stats.reliability.score < 90) {
      recommendations.push('Improve system reliability and consistency');
    }

    return recommendations;
  }

  /**
   * Helper methods for trend analysis
   */
  private _detectSeasonality(_values: number[]): boolean {
    // Simple seasonality detection - would be more sophisticated in production
    return false;
  }

  private _detectAnomalies(_values: number[]): number[] {
    // Simple anomaly detection - would use more advanced algorithms in production
    return [];
  }

  private _generateForecast(values: number[], points: number): number[] {
    // Simple linear extrapolation - would use more sophisticated models in production
    const trend = this._calculateLinearTrend(values);
    const lastIndex = values.length - 1;

    return Array.from({ length: points }, (_, i) =>
      trend.slope * (lastIndex + i + 1) + trend.intercept
    );
  }

  private _calculateConsistency(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.max(0, 100 - (Math.sqrt(variance) / mean) * 100);
  }

  /**
   * Stop all active analysis sessions
   */
  private async _stopAllActiveSessions(): Promise<void> {
    const sessionIds = Array.from(this._activeSessions.keys());

    for (const sessionId of sessionIds) {
      try {
        const session = this._activeSessions.get(sessionId);
        if (session && session.status === 'active') {
          session.status = 'completed';
          session.endTime = new Date();
        }
      } catch (error) {
        this.logError('Failed to stop analysis session during shutdown', {
          sessionId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    this.logInfo('All active analysis sessions stopped', {
      sessionCount: sessionIds.length
    });
  }

  /**
   * Cleanup expired analysis data
   */
  private _cleanupExpiredAnalysis(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    // Cleanup analysis cache
    for (const [key, result] of Array.from(this._analysisCache.entries())) {
      const resultAge = now - new Date(result.timestamp).getTime();
      if (resultAge > ANALYSIS_CONSTANTS.CLEANUP_INTERVAL) {
        expiredKeys.push(key);
      }
    }

    // Remove expired analysis results
    for (const key of expiredKeys) {
      this._analysisCache.delete(key);
    }

    if (expiredKeys.length > 0) {
      this.logInfo('Cleaned up expired analysis data', {
        expiredCount: expiredKeys.length,
        remainingCacheSize: this._analysisCache.size
      });
    }
  }
}
