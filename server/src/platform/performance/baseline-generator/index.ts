/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Performance Baseline Generator Module Exports
 * @filepath server/src/platform/performance/baseline-generator/index.ts
 * @milestone M0.1
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02
 * @component performance-baseline-generator-exports
 * @reference foundation-context
 * @template module-exports
 * @tier server
 * @context performance-context
 * @category performance-monitoring
 * @created 2025-09-13
 * @modified 2025-09-13 00:00:00 +03
 * @version 1.0.0
 *
 * @description
 * Module exports for the Performance Baseline Generator system. Provides centralized
 * access to all baseline generation components, interfaces, and types with proper
 * enterprise-grade module organization and dependency management.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact performance-architecture,module-exports
 * @milestone-compliance M0.1-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on PerformanceBaselineGenerator,BaselineGeneratorCore,PerformanceMetricsCollector,BaselineAnalysisEngine
 * @enables performance-baseline-module-access
 * @exports PerformanceBaselineGenerator,BaselineGeneratorCore,PerformanceMetricsCollector,BaselineAnalysisEngine
 * @integrates-with Enhanced Orchestration Driver v6.4.0
 * @related-contexts foundation-context, performance-context
 * @governance-impact performance-architecture, module-system
 * @api-classification internal
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level MEM-SAFE-002
 * @base-class not-applicable
 * @memory-boundaries not-applicable
 * @circular-buffer-implementation not-applicable
 * @memory-leak-prevention not-applicable
 * @resource-cleanup-strategy not-applicable
 * @timing-resilience-level not-applicable
 * @timing-requirements not-applicable
 * @timing-fallback-strategy not-applicable
 * @timing-monitoring not-applicable
 * @timing-integration not-applicable
 *
 * 🌐 GATEWAY INTEGRATION (v2.3)
 * @gateway-compatibility full
 * @gateway-endpoints not-applicable
 * @gateway-authentication not-applicable
 * @gateway-rate-limiting not-applicable
 * @gateway-monitoring not-applicable
 * @gateway-documentation auto-generated
 *
 * 🔒 SECURITY CLASSIFICATION (v2.3)
 * @security-level INTERNAL
 * @data-classification module-exports
 * @access-control internal-systems-only
 * @encryption-requirements none
 * @audit-requirements none
 * @compliance-requirements enterprise-standards
 *
 * ⚡ PERFORMANCE REQUIREMENTS (v2.3)
 * @response-time-target not-applicable
 * @throughput-target not-applicable
 * @memory-limit not-applicable
 * @cpu-limit not-applicable
 * @scalability-target not-applicable
 * @availability-target not-applicable
 *
 * 🔄 INTEGRATION REQUIREMENTS (v2.3)
 * @integration-tier server
 * @integration-pattern module-exports
 * @integration-dependencies PerformanceBaselineGenerator,BaselineGeneratorCore,PerformanceMetricsCollector,BaselineAnalysisEngine
 * @integration-endpoints not-applicable
 * @integration-monitoring not-applicable
 * @integration-fallback not-applicable
 * @integration-recovery not-applicable
 * @integration-validation not-applicable
 * @integration-documentation comprehensive
 * @integration-testing automated
 * @integration-compliance enterprise-standards
 * @integration-governance authority-driven
 * @integration-cross-reference-validated true
 *
 * ============================================================================
 */

// ============================================================================
// MAIN ORCHESTRATOR EXPORTS
// ============================================================================

export {
  PerformanceBaselineGenerator
} from './PerformanceBaselineGenerator';

// ============================================================================
// SPECIALIZED ENGINE EXPORTS
// ============================================================================

export {
  BaselineGeneratorCore
} from './BaselineGeneratorCore';

export {
  PerformanceMetricsCollector
} from './PerformanceMetricsCollector';

export {
  BaselineAnalysisEngine
} from './BaselineAnalysisEngine';

// ============================================================================
// TYPE AND INTERFACE EXPORTS
// ============================================================================

export {
  // Core interfaces
  IPerformanceBaseline,
  IMetricsCollector,
  IBaselineAnalyzer,

  // Configuration types
  TPerformanceBaselineConfig,
  TMetricsCollectionConfig,
  TAnalysisConfig,

  // Result types
  TPerformanceBaselineResult,
  TComponentBaselineResult,
  TPerformanceMetricsData,

  // Analysis types
  TPerformanceAnalysis,
  TStatisticalSummary,
  TTrendAnalysis,
  TAnalysisResult,

  // Threshold and validation types
  TPerformanceThresholds,
  TThresholdValidationResult,

  // Utility types
  TPerformanceEnvironment,
  TBaselineStatus
} from './types/performance-baseline-types';

// ============================================================================
// DEFAULT EXPORT
// ============================================================================

export { PerformanceBaselineGenerator as default } from './PerformanceBaselineGenerator';
