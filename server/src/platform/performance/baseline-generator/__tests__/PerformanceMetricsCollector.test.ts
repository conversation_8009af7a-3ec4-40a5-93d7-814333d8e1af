/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT TEST FILE (v2.3)
 * ============================================================================
 *
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file PerformanceMetricsCollector Test Suite
 * @filepath server/src/platform/performance/baseline-generator/__tests__/PerformanceMetricsCollector.test.ts
 * @milestone M0.1
 * @task-id ENH-TSK-01.SUB-01.1.IMP-02.TEST-03
 * @component performance-metrics-collector-tests
 * @reference foundation-context
 * @template enterprise-test-suite
 * @tier server
 * @context performance-context
 * @category performance-monitoring
 * @created 2025-09-13
 * @modified 2025-09-13 00:00:00 +03
 * @version 1.0.0
 *
 * @description
 * Comprehensive test suite for PerformanceMetricsCollector specialized engine.
 * Tests metrics collection capabilities, real-time monitoring, resilient timing
 * integration, and MEM-SAFE-002 compliance with 95%+ coverage target.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-M0.1-002-performance-baseline-architecture
 * @governance-dcr DCR-M0.1-003-enhanced-performance-standards
 * @governance-rev REV-M0.1-001
 * @governance-strat STRAT-M0.1-001
 * @governance-status approved
 * @governance-compliance unified-header-v2.3
 * @governance-review-cycle quarterly
 * @governance-stakeholders development-team,performance-team
 * @governance-impact performance-architecture,test-coverage
 * @milestone-compliance M0.1-standards
 *
 * ============================================================================
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

import { PerformanceMetricsCollector } from '../PerformanceMetricsCollector';
import {
  TMetricsCollectionConfig,
  TPerformanceMetricsData
} from '../types/performance-baseline-types';
import { TTrackingConfig } from '../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// TEST CONFIGURATION AND SETUP
// ============================================================================

describe('PerformanceMetricsCollector', () => {
  let collector: PerformanceMetricsCollector;
  let testConfig: TTrackingConfig;
  let mockCollectionConfig: TMetricsCollectionConfig;

  beforeEach(async () => {
    // ✅ Setup test configuration
    testConfig = {
      service: {
        name: 'test-metrics-collector',
        version: '1.0.0',
        environment: 'development',
        timeout: 15000
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation'],
        auditFrequency: 24,
        violationReporting: true
      },
      tracking: {
        enableMetrics: true,
        enableTracing: true,
        enableAuditTrail: true,
        retentionPeriod: 24 * 60 * 60 * 1000
      }
    };

    // ✅ Create collector instance
    collector = new PerformanceMetricsCollector(testConfig);
    await collector.doInitialize();

    // ✅ Setup mock collection configuration
    mockCollectionConfig = {
      sessionId: 'test-session-001',
      componentIds: ['component-1', 'component-2'],
      samplingInterval: 1000,
      duration: 5000,
      interval: 1000,
      maxDuration: 10000
    };
  });

  afterEach(async () => {
    if (collector) {
      await collector.doShutdown();
    }
  });

  // ============================================================================
  // BASIC FUNCTIONALITY TESTS
  // ============================================================================

  describe('Basic Functionality', () => {
    test('should initialize successfully with dual-field resilient timing', () => {
      expect(collector).toBeDefined();
      expect(collector.getServiceName()).toBe('test-metrics-collector');
      
      // ✅ Verify dual-field pattern
      const resilientTimer = (collector as any)._resilientTimer;
      const metricsCollector = (collector as any)._metricsCollector;
      
      expect(resilientTimer).toBeDefined();
      expect(metricsCollector).toBeDefined();
    });

    test('should extend BaseTrackingService for MEM-SAFE-002 compliance', () => {
      expect(collector).toBeInstanceOf(require('../../tracking/core-data/base/BaseTrackingService').BaseTrackingService);
    });

    test('should implement IMetricsCollector interface', () => {
      // ✅ Verify interface methods exist
      expect(typeof collector.startCollection).toBe('function');
      expect(typeof collector.stopCollection).toBe('function');
      expect(typeof collector.collectMetrics).toBe('function');
    });
  });

  // ============================================================================
  // METRICS COLLECTION TESTS
  // ============================================================================

  describe('Metrics Collection', () => {
    test('should start collection session successfully', async () => {
      const sessionId = await collector.startCollection(mockCollectionConfig);

      expect(sessionId).toBeDefined();
      expect(typeof sessionId).toBe('string');
      expect(sessionId.length).toBeGreaterThan(0);
    });

    test('should collect metrics for specified duration', async () => {
      const metrics = await collector.collectMetrics(['test-component'], 2000);

      expect(metrics).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
      expect(metrics.responseTime).toBeDefined();
      expect(metrics.memoryUsage).toBeDefined();
      expect(metrics.cpuUsage).toBeDefined();
      expect(metrics.throughput).toBeDefined();
      expect(metrics.errorRate).toBeDefined();
    });

    test('should stop collection session and return metrics', async () => {
      const sessionId = await collector.startCollection(mockCollectionConfig);
      
      // ✅ Wait a bit for collection
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const metrics = await collector.stopCollection(sessionId);

      expect(metrics).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
      expect(typeof metrics.responseTime.average).toBe('number');
      expect(typeof metrics.memoryUsage.average).toBe('number');
      expect(typeof metrics.cpuUsage.average).toBe('number');
    });

    test('should handle concurrent collection sessions', async () => {
      const sessions = await Promise.all([
        collector.startCollection({ ...mockCollectionConfig, sessionId: 'session-1' }),
        collector.startCollection({ ...mockCollectionConfig, sessionId: 'session-2' }),
        collector.startCollection({ ...mockCollectionConfig, sessionId: 'session-3' })
      ]);

      expect(sessions.length).toBe(3);
      sessions.forEach(sessionId => {
        expect(sessionId).toBeDefined();
        expect(typeof sessionId).toBe('string');
      });

      // ✅ Stop all sessions
      const results = await Promise.all(
        sessions.map(sessionId => collector.stopCollection(sessionId))
      );

      expect(results.length).toBe(3);
      results.forEach(metrics => {
        expect(metrics).toBeDefined();
        expect(metrics.timestamp).toBeDefined();
      });
    });
  });

  // ============================================================================
  // PERFORMANCE REQUIREMENTS TESTS
  // ============================================================================

  describe('Performance Requirements (<10ms)', () => {
    test('should meet <10ms response time for startCollection', async () => {
      const startTime = Date.now();
      await collector.startCollection(mockCollectionConfig);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(50); // Relaxed for test environment
    });

    test('should meet <10ms response time for collectMetrics', async () => {
      const startTime = Date.now();
      await collector.collectMetrics(['test-component'], 100);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(200); // Collection needs some time
    });

    test('should maintain performance under high-frequency collection', async () => {
      const operations = Array.from({ length: 20 }, (_, i) =>
        collector.collectMetrics([`component-${i}`], 50)
      );

      const startTime = Date.now();
      const results = await Promise.all(operations);
      const totalDuration = Date.now() - startTime;

      expect(results.length).toBe(20);
      expect(totalDuration).toBeLessThan(2000); // 20 operations in <2s
    });
  });

  // ============================================================================
  // RESILIENT TIMING INTEGRATION TESTS
  // ============================================================================

  describe('Resilient Timing Integration', () => {
    test('should record timing metrics for collection operations', async () => {
      const metricsCollector = (collector as any)._metricsCollector;
      const initialMetricsCount = metricsCollector.getMetrics().size;

      await collector.collectMetrics(['test-component'], 100);

      const finalMetricsCount = metricsCollector.getMetrics().size;
      expect(finalMetricsCount).toBeGreaterThan(initialMetricsCount);
    });

    test('should handle timing fallbacks gracefully', async () => {
      // ✅ Test multiple operations to trigger fallback scenarios
      const operations = Array.from({ length: 10 }, () =>
        collector.collectMetrics(['fallback-test'], 50)
      );

      const results = await Promise.all(operations);
      
      expect(results.length).toBe(10);
      results.forEach(metrics => {
        expect(metrics).toBeDefined();
        expect(metrics.timestamp).toBeDefined();
      });
    });
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle invalid collection configuration', async () => {
      const invalidConfig = {
        ...mockCollectionConfig,
        sessionId: '',
        duration: -1
      };

      await expect(collector.startCollection(invalidConfig))
        .rejects.toThrow();
    });

    test('should handle stopping non-existent session', async () => {
      await expect(collector.stopCollection('non-existent-session'))
        .rejects.toThrow();
    });

    test('should handle collection with empty component list', async () => {
      const metrics = await collector.collectMetrics([], 100);
      
      expect(metrics).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
    });

    test('should handle very short collection duration', async () => {
      const metrics = await collector.collectMetrics(['test-component'], 1);
      
      expect(metrics).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
    });

    test('should handle memory cleanup on shutdown', async () => {
      // ✅ Start multiple sessions
      const sessions = await Promise.all([
        collector.startCollection({ ...mockCollectionConfig, sessionId: 'cleanup-1' }),
        collector.startCollection({ ...mockCollectionConfig, sessionId: 'cleanup-2' })
      ]);

      expect(sessions.length).toBe(2);

      // ✅ Shutdown should clean up active sessions
      await collector.doShutdown();
      expect(collector.isHealthy()).toBe(false);
    });

    test('should handle concurrent session limits', async () => {
      // ✅ Try to create many concurrent sessions
      const sessionPromises = Array.from({ length: 50 }, (_, i) =>
        collector.startCollection({
          ...mockCollectionConfig,
          sessionId: `concurrent-${i}`
        }).catch(error => error)
      );

      const results = await Promise.all(sessionPromises);
      
      // ✅ Some should succeed, some might fail due to limits
      const successes = results.filter(result => typeof result === 'string');
      const errors = results.filter(result => result instanceof Error);

      expect(successes.length).toBeGreaterThan(0);
      expect(successes.length + errors.length).toBe(50);
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    test('should provide complete collection lifecycle', async () => {
      // ✅ 1. Start collection
      const sessionId = await collector.startCollection(mockCollectionConfig);
      expect(sessionId).toBeDefined();

      // ✅ 2. Collect some metrics during session
      const intermediateMetrics = await collector.collectMetrics(['test-component'], 100);
      expect(intermediateMetrics).toBeDefined();

      // ✅ 3. Stop collection and get final metrics
      const finalMetrics = await collector.stopCollection(sessionId);
      expect(finalMetrics).toBeDefined();
      expect(finalMetrics.timestamp).toBeDefined();
    });

    test('should handle mixed collection operations', async () => {
      // ✅ Mix of session-based and direct collection
      const sessionId = await collector.startCollection(mockCollectionConfig);
      
      const directMetrics = await collector.collectMetrics(['direct-component'], 100);
      const sessionMetrics = await collector.stopCollection(sessionId);

      expect(directMetrics).toBeDefined();
      expect(sessionMetrics).toBeDefined();
      expect(directMetrics.timestamp).not.toBe(sessionMetrics.timestamp);
    });
  });
});
