#!/usr/bin/env node

/**
 * ============================================================================
 * OA FRAMEWORK - HYBRID DOCUMENTATION VALIDATION SCRIPT
 * ============================================================================
 * 
 * Copyright (c) 2025 E.Z. Consultancy. All rights reserved.
 *
 * @file Hybrid Documentation Cross-Reference Validation
 * @filepath scripts/validate-hybrid-documentation.js
 * @milestone M0.1
 * @task-id M0.1-DCR-006-validation
 * @component hybrid-documentation-validator
 * @reference foundation-context.DOCUMENTATION.006
 * @template scripts/validation/validation-script-standard.template
 * @tier T0
 * @context foundation-context
 * @category Documentation Validation
 * @created 2025-09-13
 * @modified 2025-09-13
 * @version 1.0.0
 *
 * @description
 * Validates cross-reference integrity between implementation and governance
 * documentation layers in the OA Framework hybrid structure.
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level development-standards
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-dcr DCR-M0.1-006-hybrid-documentation-structure-authorization
 * @governance-status APPROVED
 * @governance-compliance HYBRID_STRUCTURE_AUTHORIZED
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on fs, path, glob
 * @integrates-with hybrid-documentation-structure
 * @cross-references DCR-M0.1-006, development-standards-v21
 *
 * 📝 VERSION HISTORY
 * v1.0.0 (2025-09-13) - Initial implementation with cross-reference validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class HybridDocumentationValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.validatedLinks = 0;
    this.brokenLinks = 0;
    
    this.implementationLayer = 'docs/contexts';
    this.governanceLayer = 'docs/governance/contexts';
    
    this.navigationIndexes = [
      'docs/contexts/foundation-context/GOVERNANCE-WORKFLOW-INDEX.md',
      'docs/governance/contexts/foundation-context/IMPLEMENTATION-DOCUMENTATION-INDEX.md'
    ];
  }

  /**
   * Main validation entry point
   */
  async validateHybridStructure() {
    console.log('🔍 OA Framework Hybrid Documentation Validation');
    console.log('================================================\n');
    
    try {
      await this.validateDirectoryStructure();
      await this.validateNavigationIndexes();
      await this.validateCrossReferences();
      await this.validateFileIntegrity();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Validate required directory structure exists
   */
  async validateDirectoryStructure() {
    console.log('📁 Validating Directory Structure...');
    
    const requiredDirs = [
      'docs/contexts/foundation-context',
      'docs/governance/contexts/foundation-context',
      'docs/contexts/foundation-context/api',
      'docs/contexts/foundation-context/components',
      'docs/contexts/foundation-context/guides',
      'docs/contexts/foundation-context/services',
      'docs/governance/contexts/foundation-context/02-adr',
      'docs/governance/contexts/foundation-context/03-dcr'
    ];

    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        this.errors.push(`Missing required directory: ${dir}`);
      }
    }

    console.log(`✅ Directory structure validation complete\n`);
  }

  /**
   * Validate navigation index files exist and are properly formatted
   */
  async validateNavigationIndexes() {
    console.log('🧭 Validating Navigation Indexes...');
    
    for (const indexFile of this.navigationIndexes) {
      if (!fs.existsSync(indexFile)) {
        this.errors.push(`Missing navigation index: ${indexFile}`);
        continue;
      }

      const content = fs.readFileSync(indexFile, 'utf8');
      
      // Validate index contains required sections
      const requiredSections = [
        'NAVIGATION OVERVIEW',
        'LOCATIONS',
        'RELATIONSHIP MAP'
      ];

      for (const section of requiredSections) {
        if (!content.includes(section)) {
          this.warnings.push(`Navigation index ${indexFile} missing section: ${section}`);
        }
      }
    }

    console.log(`✅ Navigation index validation complete\n`);
  }

  /**
   * Validate cross-references between layers
   */
  async validateCrossReferences() {
    console.log('🔗 Validating Cross-References...');
    
    // Find all markdown files in both layers
    const implementationFiles = this.findMarkdownFiles(this.implementationLayer);
    const governanceFiles = this.findMarkdownFiles(this.governanceLayer);
    
    // Validate links from implementation to governance
    for (const file of implementationFiles) {
      await this.validateLinksInFile(file, 'implementation-to-governance');
    }
    
    // Validate links from governance to implementation
    for (const file of governanceFiles) {
      await this.validateLinksInFile(file, 'governance-to-implementation');
    }

    console.log(`✅ Cross-reference validation complete\n`);
  }

  /**
   * Find all markdown files in a directory
   */
  findMarkdownFiles(directory) {
    const files = [];
    
    if (!fs.existsSync(directory)) {
      return files;
    }

    const walk = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          walk(fullPath);
        } else if (item.endsWith('.md')) {
          files.push(fullPath);
        }
      }
    };

    walk(directory);
    return files;
  }

  /**
   * Validate links in a specific file
   */
  async validateLinksInFile(filePath, direction) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Find markdown links [text](path)
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    let match;
    
    while ((match = linkRegex.exec(content)) !== null) {
      const linkText = match[1];
      const linkPath = match[2];
      
      // Skip external links
      if (linkPath.startsWith('http') || linkPath.startsWith('mailto:')) {
        continue;
      }
      
      // Resolve relative path
      const resolvedPath = path.resolve(path.dirname(filePath), linkPath);
      
      this.validatedLinks++;
      
      // Check if target exists
      if (!fs.existsSync(resolvedPath)) {
        this.brokenLinks++;
        this.errors.push(`Broken link in ${filePath}: [${linkText}](${linkPath})`);
      }
    }
  }

  /**
   * Validate file integrity and required content
   */
  async validateFileIntegrity() {
    console.log('📋 Validating File Integrity...');
    
    // Validate DCR-M0.1-006 exists and is approved
    const dcrPath = 'docs/governance/contexts/foundation-context/03-dcr/DCR-M0.1-006-hybrid-documentation-structure-authorization.md';
    
    if (!fs.existsSync(dcrPath)) {
      this.errors.push('Missing required DCR-M0.1-006 authorization document');
    } else {
      const dcrContent = fs.readFileSync(dcrPath, 'utf8');
      if (!dcrContent.includes('status: APPROVED')) {
        this.errors.push('DCR-M0.1-006 is not approved');
      }
    }

    // Validate Development Standards v21 includes hybrid structure
    const devStandardsPath = 'docs/core/development-standards-v21.md';
    
    if (fs.existsSync(devStandardsPath)) {
      const content = fs.readFileSync(devStandardsPath, 'utf8');
      if (!content.includes('Hybrid Documentation Structure')) {
        this.warnings.push('Development Standards v21 may not include hybrid structure documentation');
      }
    }

    console.log(`✅ File integrity validation complete\n`);
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('📊 VALIDATION REPORT');
    console.log('====================\n');
    
    console.log(`📈 Statistics:`);
    console.log(`   Links Validated: ${this.validatedLinks}`);
    console.log(`   Broken Links: ${this.brokenLinks}`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Warnings: ${this.warnings.length}\n`);
    
    if (this.errors.length > 0) {
      console.log('❌ ERRORS:');
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
      console.log('');
    }
    
    if (this.warnings.length > 0) {
      console.log('⚠️  WARNINGS:');
      this.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`);
      });
      console.log('');
    }
    
    if (this.errors.length === 0) {
      console.log('✅ VALIDATION PASSED: Hybrid documentation structure is valid');
    } else {
      console.log('❌ VALIDATION FAILED: Issues found in hybrid documentation structure');
      process.exit(1);
    }
  }
}

// CLI interface
if (require.main === module) {
  const validator = new HybridDocumentationValidator();
  validator.validateHybridStructure().catch(error => {
    console.error('Validation error:', error);
    process.exit(1);
  });
}

module.exports = HybridDocumentationValidator;
